import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGhnFieldsToUserShopInfo1734567890000 implements MigrationInterface {
  name = 'AddGhnFieldsToUserShopInfo1734567890000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "user_shop_info" 
      ADD COLUMN "ghn_district_id" integer NULL COMMENT 'District ID cho GHN API'
    `);

    await queryRunner.query(`
      ALTER TABLE "user_shop_info" 
      ADD COLUMN "ghn_ward_code" varchar(20) NULL COMMENT 'Ward Code cho GHN API'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "user_shop_info" 
      DROP COLUMN "ghn_ward_code"
    `);

    await queryRunner.query(`
      ALTER TABLE "user_shop_info" 
      DROP COLUMN "ghn_district_id"
    `);
  }
}
