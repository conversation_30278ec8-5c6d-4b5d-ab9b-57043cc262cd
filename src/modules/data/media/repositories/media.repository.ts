import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { Media } from '../entities/media.entity';
import { QueryDto } from '@common/dto';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { MediaQueryDto } from '../dto/media-query.dto';
import { MediaStatusEnum } from '../enums/media-status.enum';
import { AppException, ErrorCode } from '@/common';
import { AdminMediaResponseDto } from '../dto/media-admin.dto';
import { plainToInstance } from 'class-transformer';
import { MEDIA_ERROR_CODES } from '../exception';

@Injectable()
export class MediaRepository extends Repository<Media> {
  private readonly logger = new Logger(MediaRepository.name);

  constructor(private dataSource: DataSource) {
    super(Media, dataSource.createEntityManager());
  }

  async findAllUserMedia(
    userId: number,
    query: MediaQueryDto,
  ): Promise<PaginatedResult<Media>> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = query;

    // Tạo query builder
    const queryBuilder = this.createQueryBuilder('media').where(
      'media.ownedBy = :userId',
      { userId },
    );

    // Thêm các điều kiện search nếu có
    if (query.search && query.search !== 'keyword') {
      queryBuilder.andWhere(
        'media.name ILIKE :search OR media.description ILIKE :search',
        { search: `%${query.search}%` },
      );
    }

    // Mặc định lấy các media có trạng thái PENDING hoặc APPROVED
    queryBuilder.andWhere('media.status IN (:...statuses)', {
      statuses: [MediaStatusEnum.PENDING, MediaStatusEnum.APPROVED],
    });

    // Không lấy ra ownedBy
    queryBuilder.select([
      'media.id',
      'media.name',
      'media.description',
      'media.size',
      'media.tags',
      'media.storageKey',
      'media.createdAt',
      'media.updatedAt',
      'media.nameEmbedding',
      'media.descriptionEmbedding',
      'media.status'
    ]);

    // Sắp xếp
    queryBuilder.orderBy(`media.${sortBy}`, sortDirection);

    // Phân trang
    queryBuilder.skip((page - 1) * limit).take(limit);

    // Lấy dữ liệu và tổng số bản ghi
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    // Tạo kết quả phân trang
    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  async findAllForAdmin(
    query: MediaQueryDto,
  ): Promise<PaginatedResult<AdminMediaResponseDto>> {
    const page: number = query.page ?? 1;
    const limit: number = query.limit ?? 10;

    const queryBuilder = this.createQueryBuilder('media')
      .leftJoin('users', 'user', 'user.id = media.owned_by')
      .select([
        'media.id',
        'media.name',
        'media.description',
        'media.size',
        'media.tags',
        'media.storageKey',
        'media.createdAt',
        'media.updatedAt',
        'media.nameEmbedding',
        'media.descriptionEmbedding',
        'media.status'
      ])
      .addSelect([
        'user.full_name AS user_full_name',
        'user.avatar AS user_avatar',
        'user.id AS user_id',
      ])
      .where('media.status != :deletedStatus', { deletedStatus: MediaStatusEnum.DELETED });

    if (query.search && query.search !== 'keyword') {
      queryBuilder.andWhere(
        'media.name ILIKE :search OR media.description ILIKE :search',
        { search: `%${query.search}%` },
      );
    }

    if (query.status) {
      queryBuilder.andWhere('media.status = :status', { status: query.status });
    }

    // Use createdAt as default sort field if sortBy is not provided
    const sortBy = query.sortBy || 'createdAt';
    queryBuilder.orderBy(`media.${sortBy}`, query.sortDirection);
    queryBuilder.skip((page - 1) * limit).take(limit);

    const rawItems = await queryBuilder.getRawMany(); // 👈 raw chứa full_name và avatar
    const mediaEntities = await queryBuilder.getMany(); // 👈 media entity chính

    const items = mediaEntities.map((media, index) => {
      const raw = rawItems[index];
      const dto = plainToInstance(AdminMediaResponseDto, media);
      dto.author = raw.user_full_name ?? 'Ẩn danh';
      dto.avatar = raw.user_avatar ?? undefined;
      dto.authorId = raw.user_id ? Number(raw.user_id) : undefined;
      return dto;
    });

    const totalItems = await queryBuilder.getCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm kiếm media theo ID
   * @param id ID của media
   * @returns
   */
  async findByIdConfig(id: string): Promise<Media | null> {
    const queryBuilder = this.createQueryBuilder('media')
      .leftJoin('users', 'user', 'user.id = media.owned_by')
      .select([
        'media.id',
        'media.name',
        'media.description',
        'media.size',
        'media.tags',
        'media.storageKey',
        'media.createdAt',
        'media.updatedAt',
        'media.nameEmbedding',
        'media.descriptionEmbedding',
        'media.status',
        'media.ownedBy'
      ])
      .addSelect([
        'user.full_name AS user_full_name',
        'user.avatar AS user_avatar',
        'user.id AS user_id',
      ])
      .where('media.id = :id', { id })
      .andWhere('media.status != :deletedStatus', { deletedStatus: MediaStatusEnum.DELETED });

    const rawResult = await queryBuilder.getRawOne();
    const mediaEntity = await queryBuilder.getOne();

    if (!mediaEntity) return null;

    if (rawResult) {
      const dto = plainToInstance(AdminMediaResponseDto, mediaEntity);
      dto.author = rawResult.user_full_name ?? 'Ẩn danh';
      dto.avatar = rawResult.user_avatar ?? undefined;
      dto.authorId = rawResult.user_id ? Number(rawResult.user_id) : undefined;
      return dto;
    }

    return mediaEntity;
  }

  /**
   * Tìm kiếm media theo danh sách ID
   * @param ids Danh sách ID của media
   * @returns Danh sách media tìm thấy
   */
  async findByIds(ids: string[]): Promise<Media[]> {
    return this.createQueryBuilder('media')
      .select([
        'media.id',
        'media.name',
        'media.description',
        'media.size',
        'media.tags',
        'media.storageKey',
        'media.createdAt',
        'media.updatedAt',
        'media.nameEmbedding',
        'media.descriptionEmbedding',
        'media.status'
      ])
      .where('media.id IN (:...ids)', { ids })
      .andWhere('media.status != :deletedStatus', { deletedStatus: MediaStatusEnum.DELETED })
      .getMany();
  }

  /**
   * Tính tổng dung lượng media của một người dùng
   * @param userId ID của người dùng
   * @returns Tổng dung lượng (bytes) và số lượng files
   */
  async getTotalSizeByUser(userId: number): Promise<{ totalSize: number; fileCount: number }> {
    try {
      const result = await this.createQueryBuilder('media')
        .select('SUM(media.size)', 'totalSize')
        .addSelect('COUNT(media.id)', 'fileCount')
        .where('media.ownedBy = :userId', { userId })
        .andWhere('media.status IN (:...statuses)', {
          statuses: [MediaStatusEnum.PENDING, MediaStatusEnum.APPROVED],
        })
        .getRawOne();

      return {
        totalSize: parseInt(result?.totalSize || '0'),
        fileCount: parseInt(result?.fileCount || '0'),
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tính tổng dung lượng media của user ${userId}: ${error.message}`, error.stack);
      return { totalSize: 0, fileCount: 0 };
    }
  }
}
