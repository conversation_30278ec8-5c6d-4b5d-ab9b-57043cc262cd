import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@modules/user/entities/user.entity';
import { KnowledgeFile } from '@modules/data/knowledge-files/entities/knowledge-file.entity';
import { Media } from '@modules/data/media/entities/media.entity';
import { Url } from '@modules/data/url/entities/url.entity';
import { VectorStore } from '@modules/data/knowledge-files/entities/vector-store.entity';
import { StatisticsResponseDto, UserStorageResponseDto, StorageBreakdownDto } from '../dto/statistics-response.dto';
import { plainToInstance } from 'class-transformer';
import { MediaRepository } from '@modules/data/media/repositories';
import { KnowledgeFileRepository, VectorStoreRepository } from '@modules/data/knowledge-files/repositories';
import { FileSizeFormatter } from '@shared/utils';

/**
 * Service xử lý các thống kê dữ liệu trong hệ thống
 * <PERSON>ung cấp các phương thức để lấy thống kê về số lượng người dùng, knowledge file, media, URL và vector store
 */
@Injectable()
export class StatisticsService {
  private readonly logger = new Logger(StatisticsService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(KnowledgeFile)
    private readonly knowledgeFileRepository: Repository<KnowledgeFile>,
    @InjectRepository(Media)
    private readonly mediaRepository: Repository<Media>,
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    @InjectRepository(VectorStore)
    private readonly vectorStoreRepository: Repository<VectorStore>,
    private readonly mediaRepo: MediaRepository,
    private readonly knowledgeFileRepo: KnowledgeFileRepository,
    private readonly vectorStoreRepo: VectorStoreRepository,
  ) {}

  /**
   * Lấy thống kê tổng số lượng người dùng, knowledge file, media, URL và vector store
   * @returns Thống kê dữ liệu
   */
  async getStatistics(): Promise<StatisticsResponseDto> {
    try {
      this.logger.log('Đang lấy thống kê dữ liệu...');

      // Thực hiện các truy vấn đếm song song để tối ưu hiệu suất
      const [totalUsers, totalKnowledgeFiles, totalMedia, totalUrls, totalVectorStores] = await Promise.all([
        this.userRepository.count(),
        this.knowledgeFileRepository.count(),
        this.mediaRepository.count(),
        this.urlRepository.count(),
        this.vectorStoreRepository.count(),
      ]);

      this.logger.log(`Thống kê: Users=${totalUsers}, KnowledgeFiles=${totalKnowledgeFiles}, Media=${totalMedia}, URLs=${totalUrls}, VectorStores=${totalVectorStores}`);

      // Chuyển đổi sang DTO
      return plainToInstance(
        StatisticsResponseDto,
        {
          totalUsers,
          totalKnowledgeFiles,
          totalMedia,
          totalUrls,
          totalVectorStores,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê dữ liệu: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thống kê dung lượng dữ liệu của một người dùng
   * @param userId ID của người dùng
   * @returns Thống kê dung lượng dữ liệu
   */
  async getUserStorageUsage(userId: number): Promise<UserStorageResponseDto> {
    try {
      this.logger.log(`Đang lấy thống kê dung lượng dữ liệu cho user ${userId}...`);

      // Thực hiện các truy vấn tính tổng dung lượng song song để tối ưu hiệu suất
      const [mediaStats, knowledgeFileStats, vectorStoreStats] = await Promise.all([
        this.mediaRepo.getTotalSizeByUser(userId),
        this.knowledgeFileRepo.getTotalStorageByUser(userId),
        this.vectorStoreRepo.getTotalStorageByUser(userId),
      ]);

      // Tính tổng dung lượng
      const totalUsed = mediaStats.totalSize + knowledgeFileStats.totalSize + vectorStoreStats.totalSize;

      // Tạo breakdown
      const breakdown = plainToInstance(
        StorageBreakdownDto,
        {
          mediaSize: mediaStats.totalSize,
          knowledgeFileSize: knowledgeFileStats.totalSize,
          vectorStoreSize: vectorStoreStats.totalSize,
        },
        { excludeExtraneousValues: true },
      );

      // Tạo file counts
      const fileCounts = {
        mediaFiles: mediaStats.fileCount,
        knowledgeFiles: knowledgeFileStats.fileCount,
        vectorStores: vectorStoreStats.storeCount,
      };

      this.logger.log(
        `Thống kê dung lượng user ${userId}: Total=${FileSizeFormatter.formatBytes(totalUsed)}, ` +
        `Media=${FileSizeFormatter.formatBytes(mediaStats.totalSize)} (${mediaStats.fileCount} files), ` +
        `Knowledge=${FileSizeFormatter.formatBytes(knowledgeFileStats.totalSize)} (${knowledgeFileStats.fileCount} files), ` +
        `VectorStore=${FileSizeFormatter.formatBytes(vectorStoreStats.totalSize)} (${vectorStoreStats.storeCount} stores)`
      );

      // Chuyển đổi sang DTO
      return plainToInstance(
        UserStorageResponseDto,
        {
          totalUsed,
          totalUsedFormatted: FileSizeFormatter.formatBytes(totalUsed),
          breakdown,
          fileCounts,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê dung lượng dữ liệu cho user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
