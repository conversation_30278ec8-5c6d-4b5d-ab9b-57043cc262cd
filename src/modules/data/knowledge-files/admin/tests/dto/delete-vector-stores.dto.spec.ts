import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { DeleteVectorStoresDto } from '../../dto/delete-vector-stores.dto';

describe('DeleteVectorStoresDto', () => {
  it('nên xác thực DTO hợp lệ với nhiều vector store ID', async () => {
    // Arrange
    const dto = plainToInstance(DeleteVectorStoresDto, {
      storeIds: ['vs_123e4567-e89b-12d3-a456-426614174000', 'vs_123e4567-e89b-12d3-a456-426614174001']
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với một vector store ID', async () => {
    // Arrange
    const dto = plainToInstance(DeleteVectorStoresDto, {
      storeIds: ['vs_123e4567-e89b-12d3-a456-426614174000']
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi storeIds là mảng rỗng', async () => {
    // Arrange
    const dto = plainToInstance(DeleteVectorStoresDto, {
      storeIds: []
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('arrayMinSize');
  });

  it('nên thất bại khi storeIds không được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(DeleteVectorStoresDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isArray');
  });

  it('nên thất bại khi storeIds chứa chuỗi rỗng', async () => {
    // Arrange
    const dto = plainToInstance(DeleteVectorStoresDto, {
      storeIds: ['vs_123e4567-e89b-12d3-a456-426614174000', '']
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi storeIds chứa giá trị không phải chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(DeleteVectorStoresDto, {
      storeIds: ['vs_123e4567-e89b-12d3-a456-426614174000', 123]
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });
});
