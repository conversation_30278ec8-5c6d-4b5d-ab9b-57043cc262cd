import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { UserProviderShipmentService } from '../services/user-provider-shipment.service';
import { UserProviderConfigValidationService } from '../services/user-provider-config-validation.service';
import { ProviderShipmentType } from '../constants/provider-shipment-type.enum';
import {
  CreateUserProviderShipmentDto,
  UpdateUserProviderShipmentDto,
  UserProviderShipmentResponseDto
} from '../dto/user-provider-shipment';
import { PaginatedResult } from '@common/response';

/**
 * Controller quản lý cấu hình provider shipment của user
 */
@ApiTags('User Provider Shipment')
@ApiBearerAuth()
@UseGuards(JwtUserGuard)
@Controller('v1/user/provider-shipments')
export class UserProviderShipmentController {
  private readonly logger = new Logger(UserProviderShipmentController.name);

  constructor(
    private readonly userProviderShipmentService: UserProviderShipmentService,
    private readonly validationService: UserProviderConfigValidationService
  ) {}

  /**
   * Tạo cấu hình provider shipment mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo cấu hình provider shipment mới',
    description: 'Tạo cấu hình mới cho GHTK hoặc GHN với thông tin API credentials'
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo cấu hình thành công',
    type: UserProviderShipmentResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ'
  })
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CreateUserProviderShipmentDto
  ): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`User ${user.id} tạo cấu hình ${dto.type}`);
      return await this.userProviderShipmentService.create(user.id, dto);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo cấu hình:`, error);
      throw error;
    }
  }

  /**
   * Lấy danh sách cấu hình provider shipment
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách cấu hình provider shipment',
    description: 'Lấy danh sách tất cả cấu hình provider shipment của user với phân trang'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Số trang (mặc định: 1)',
    example: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng item mỗi trang (mặc định: 10)',
    example: 10
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ProviderShipmentType,
    description: 'Lọc theo loại provider'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: 'PaginatedResult<UserProviderShipmentResponseDto>'
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('type') type?: ProviderShipmentType
  ): Promise<PaginatedResult<UserProviderShipmentResponseDto>> {
    try {
      this.logger.log(`User ${user.id} lấy danh sách cấu hình`);
      return await this.userProviderShipmentService.findByUserId(user.id, page, limit, type);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách cấu hình:`, error);
      throw error;
    }
  }

  /**
   * Lấy cấu hình provider shipment theo ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy cấu hình provider shipment theo ID',
    description: 'Lấy chi tiết cấu hình provider shipment theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của cấu hình',
    example: 'uuid-string'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy cấu hình thành công',
    type: UserProviderShipmentResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy cấu hình'
  })
  async findById(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string
  ): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`User ${user.id} lấy cấu hình ${id}`);
      return await this.userProviderShipmentService.findById(user.id, id);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật cấu hình provider shipment
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật cấu hình provider shipment',
    description: 'Cập nhật thông tin cấu hình provider shipment'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của cấu hình',
    example: 'uuid-string'
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật cấu hình thành công',
    type: UserProviderShipmentResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy cấu hình'
  })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() dto: UpdateUserProviderShipmentDto
  ): Promise<UserProviderShipmentResponseDto> {
    try {
      this.logger.log(`User ${user.id} cập nhật cấu hình ${id}`);
      return await this.userProviderShipmentService.update(user.id, id, dto);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật cấu hình:`, error);
      throw error;
    }
  }

  /**
   * Xóa cấu hình provider shipment
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa cấu hình provider shipment',
    description: 'Xóa cấu hình provider shipment theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của cấu hình',
    example: 'uuid-string'
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa cấu hình thành công'
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy cấu hình'
  })
  async delete(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string
  ): Promise<{ message: string }> {
    try {
      this.logger.log(`User ${user.id} xóa cấu hình ${id}`);
      await this.userProviderShipmentService.delete(user.id, id);
      return { message: 'Xóa cấu hình thành công' };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa cấu hình:`, error);
      throw error;
    }
  }

  /**
   * Lấy danh sách loại provider đã cấu hình
   */
  @Get('configured/types')
  @ApiOperation({
    summary: 'Lấy danh sách loại provider đã cấu hình',
    description: 'Lấy danh sách các loại provider mà user đã cấu hình'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    schema: {
      type: 'object',
      properties: {
        types: {
          type: 'array',
          items: {
            type: 'string',
            enum: Object.values(ProviderShipmentType)
          }
        }
      }
    }
  })
  async getConfiguredTypes(
    @CurrentUser() user: JwtPayload
  ): Promise<{ types: ProviderShipmentType[] }> {
    try {
      this.logger.log(`User ${user.id} lấy danh sách loại provider đã cấu hình`);
      
      // Lấy tất cả cấu hình của user
      const result = await this.userProviderShipmentService.findByUserId(user.id, 1, 100);
      
      // Lấy unique types
      const types = [...new Set(result.items.map(item => item.type))];
      
      return { types };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách loại provider:`, error);
      throw error;
    }
  }

  /**
   * Validate cấu hình GHTK
   */
  @Post('validate/ghtk')
  @ApiOperation({
    summary: 'Validate cấu hình GHTK',
    description: 'Kiểm tra tính hợp lệ của cấu hình GHTK bằng cách test API'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả validation',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        details: { type: 'object' }
      }
    }
  })
  async validateGHTK(@CurrentUser() user: JwtPayload) {
    try {
      this.logger.log(`User ${user.id} validate cấu hình GHTK`);
      return await this.validationService.validateGHTKConfig(user.id);
    } catch (error) {
      this.logger.error(`Lỗi khi validate GHTK:`, error);
      throw error;
    }
  }

  /**
   * Validate cấu hình GHN
   */
  @Post('validate/ghn')
  @ApiOperation({
    summary: 'Validate cấu hình GHN',
    description: 'Kiểm tra tính hợp lệ của cấu hình GHN bằng cách test API'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả validation',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        details: { type: 'object' }
      }
    }
  })
  async validateGHN(@CurrentUser() user: JwtPayload) {
    try {
      this.logger.log(`User ${user.id} validate cấu hình GHN`);
      return await this.validationService.validateGHNConfig(user.id);
    } catch (error) {
      this.logger.error(`Lỗi khi validate GHN:`, error);
      throw error;
    }
  }

  /**
   * Validate tất cả cấu hình
   */
  @Post('validate/all')
  @ApiOperation({
    summary: 'Validate tất cả cấu hình',
    description: 'Kiểm tra tính hợp lệ của tất cả cấu hình provider'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả validation tất cả provider',
    schema: {
      type: 'object',
      properties: {
        ghtk: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            details: { type: 'object' }
          }
        },
        ghn: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            details: { type: 'object' }
          }
        }
      }
    }
  })
  async validateAll(@CurrentUser() user: JwtPayload) {
    try {
      this.logger.log(`User ${user.id} validate tất cả cấu hình`);
      return await this.validationService.validateAllConfigs(user.id);
    } catch (error) {
      this.logger.error(`Lỗi khi validate tất cả cấu hình:`, error);
      throw error;
    }
  }


}
