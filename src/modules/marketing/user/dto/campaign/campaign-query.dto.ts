import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString } from 'class-validator';
import { QueryDto } from '@/common/dto/query.dto';
import { CampaignStatus } from './update-campaign.dto';

/**
 * DTO cho query parameters khi lấy danh sách campaign
 */
export class CampaignQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái campaign
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái campaign',
    enum: CampaignStatus,
    example: CampaignStatus.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(CampaignStatus)
  status?: CampaignStatus;

  /**
   * Lọc theo platform
   */
  @ApiProperty({
    description: 'Lọc theo platform',
    example: 'email',
    required: false,
  })
  @IsOptional()
  @IsString()
  platform?: string;

  /**
   * Tìm kiếm theo tiêu đề campaign
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tiêu đề campaign',
    example: 'khuyến mãi',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;
}
