import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng affiliate_rank trong cơ sở dữ liệu
 * Bảng rank của affiliate
 */
@Entity('affiliate_rank')
export class AffiliateRank {
  /**
   * Mã rank
   */
  @PrimaryGeneratedColumn({ name: 'id', comment: 'mã rank' })
  id: number;

  /**
   * Phần trăm hoa hồng
   */
  @Column({
    name: 'commission',
    type: 'float',
    nullable: true,
    comment: 'Phần trăm hoa hồng'
  })
  commission: number;

  /**
   * Số lượng người giới thiệu tối thiểu
   */
  @Column({
    name: 'min_condition',
    type: 'bigint',
    nullable: true,
    comment: 'Số lượng người giới thiệu tối thiểu'
  })
  minCondition: number;

  /**
   * Số lượng người giới thiệu tối đa
   */
  @Column({
    name: 'max_condition',
    type: 'bigint',
    nullable: true,
    comment: '<PERSON><PERSON> lượng người giới thiệu tối đa'
  })
  maxCondition: number;

  /**
   * Tên rank
   */
  @Column({
    name: 'rank_name',
    length: 45,
    nullable: true,
    comment: 'Tên rank'
  })
  rankName: string;

  /**
   * Logo rank
   */
  @Column({
    name: 'rank_badge',
    length: 255,
    nullable: true,
    comment: 'Logo rank'
  })
  rankBadge: string;

  /**
   * Trạng thái kích hoạt
   * Lưu ý: Cột này chưa có trong database, cần thêm migration để tạo cột này
   */
  @Column({
    name: 'is_active',
    type: 'boolean',
    default: true,
    comment: 'Trạng thái kích hoạt của rank'
  })
  isActive: boolean;

  /**
   * Thứ tự hiển thị
   * Lưu ý: Cột này chưa có trong database, cần thêm migration để tạo cột này
   */
  @Column({
    name: 'display_order',
    type: 'integer',
    default: 0,
    comment: 'Thứ tự hiển thị của rank'
  })
  displayOrder: number;

  /**
   * Mô tả rank
   * Lưu ý: Cột này chưa có trong database, cần thêm migration để tạo cột này
   */
  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
    comment: 'Mô tả chi tiết về rank'
  })
  description: string;

  /**
   * Thời gian tạo rank (Unix timestamp)
   * Lưu ý: Cột này chưa có trong database, cần thêm migration để tạo cột này
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian tạo rank (Unix timestamp)'
  })
  createdAt: number;

  /**
   * Thời gian cập nhật rank (Unix timestamp)
   * Lưu ý: Cột này chưa có trong database, cần thêm migration để tạo cột này
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian cập nhật rank (Unix timestamp)'
  })
  updatedAt: number;
}
