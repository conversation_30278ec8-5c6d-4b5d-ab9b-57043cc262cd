import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { AuthorTypeEnum, BlogStatusEnum } from '../../enums';

export class AuthorDto {
  @Expose()
  @ApiProperty({
    description: 'ID của tác giả',
    example: 1,
    nullable: true,
  })
  id: number | null;

  @Expose()
  @ApiProperty({
    description: 'Tên tác giả',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Loại tác giả',
    example: AuthorTypeEnum.USER,
    enum: AuthorTypeEnum,
  })
  type: AuthorTypeEnum;

  @Expose()
  @ApiProperty({
    description: 'Avatar của tác giả',
    example: 'https://cdn.example.com/avatars/user10.jpg',
    nullable: true,
  })
  avatar: string;
}

export class BlogDto {
  @Expose()
  @ApiProperty({
    description: 'ID của bài viết',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Tiêu đề bài viết',
    example: 'Tiêu đề bài viết',
  })
  title: string;

  @Expose()
  @ApiProperty({
    description: 'URL file content trên CDN',
    example: 'URL file content trên CDN',
  })
  content: string;

  @Expose()
  @ApiProperty({
    description: 'Số point',
    example: 100,
  })
  point: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượt xem',
    example: 150,
  })
  view_count: number;

  @Expose()
  @ApiProperty({
    description: 'URL thumbnail',
    example: 'URL thumbnail',
  })
  thumbnail_url: string;

  @Expose()
  @ApiProperty({
    description: 'Tags',
    example: ['tag1', 'tag2'],
    type: [String],
  })
  tags: string[];

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1632474086123,
  })
  created_at: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1632474086123,
  })
  updated_at: number;

  @Expose()
  @Type(() => AuthorDto)
  @ApiProperty({
    description: 'Thông tin tác giả',
    type: AuthorDto,
  })
  author: AuthorDto;

  @Expose()
  @ApiProperty({
    description: 'Nhân viên kiểm duyệt bài viết',
    example: null,
    nullable: true,
  })
  employee_moderator: number | null;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái của bài viết',
    example: BlogStatusEnum.APPROVED,
    enum: BlogStatusEnum,
  })
  status: BlogStatusEnum;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái hiển thị của bài viết',
    example: true,
  })
  enable: boolean;

  @Expose()
  @ApiProperty({
    description: 'Số lượt like',
    example: 45,
  })
  like: number;
}

