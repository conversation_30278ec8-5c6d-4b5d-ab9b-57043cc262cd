import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@dto/query.dto';

/**
 * DTO cho các tham số truy vấn danh sách bản ghi chuyển đổi khách hàng
 */
export class QueryUserConvertDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'ID khách hàng được chuyển đổi',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  convertCustomerId?: number;

  @ApiPropertyOptional({
    description: 'ID người dùng thực hiện chuyển đổi',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  userId?: number;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> chuyển đổi (ví dụ: online, offline, referral)',
    example: 'online'
  })
  @IsOptional()
  @IsString()
  conversionType?: string;

  @ApiPropertyOptional({
    description: 'Nguồn gốc chuyển đổi (ví dụ: website, social media, event)',
    example: 'website'
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({
    description: 'Thời gian tạo từ (timestamp)',
    example: 1625097600000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtFrom?: number;

  @ApiPropertyOptional({
    description: 'Thời gian tạo đến (timestamp)',
    example: 1625184000000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtTo?: number;
}
