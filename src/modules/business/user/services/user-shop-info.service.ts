import { Injectable, Logger } from '@nestjs/common';
import { UserShopInfoRepository } from '@modules/business/repositories/user-shop-info.repository';
import { UserShopInfoDto, UserShopInfoResponseDto, UpdateUserShopInfoDto } from '../dto/user-shop-info.dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý thông tin cửa hàng của người dùng
 */
@Injectable()
export class UserShopInfoService {
  private readonly logger = new Logger(UserShopInfoService.name);

  constructor(
    private readonly userShopInfoRepository: UserShopInfoRepository,
  ) {}

  /**
   * Lấy thông tin shop của user
   * @param userId ID người dùng
   * @returns Thông tin shop
   */
  async getShopInfo(userId: number): Promise<UserShopInfoResponseDto | null> {
    try {
      this.logger.log(`Lấy thông tin shop cho userId: ${userId}`);
      
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      
      if (!shopInfo) {
        return null;
      }

      return plainToInstance(UserShopInfoResponseDto, shopInfo, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy thông tin shop: ${error.message}`
      );
    }
  }

  /**
   * Tạo hoặc cập nhật thông tin shop
   * @param userId ID người dùng
   * @param shopInfoDto Dữ liệu shop
   * @returns Thông tin shop đã lưu
   */
  @Transactional()
  async upsertShopInfo(userId: number, shopInfoDto: UserShopInfoDto): Promise<UserShopInfoResponseDto> {
    try {
      this.logger.log(`Upsert thông tin shop cho userId: ${userId}`);
      
      const shopInfo = await this.userShopInfoRepository.upsert(userId, {
        userId,
        shopName: shopInfoDto.shopName,
        shopPhone: shopInfoDto.shopPhone,
        shopAddress: shopInfoDto.shopAddress,
        shopProvince: shopInfoDto.shopProvince,
        shopDistrict: shopInfoDto.shopDistrict,
        shopWard: shopInfoDto.shopWard,
      });

      this.logger.log(`Đã upsert thông tin shop cho userId: ${userId}`);
      return plainToInstance(UserShopInfoResponseDto, shopInfo, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi upsert thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Lỗi khi lưu thông tin shop: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật thông tin shop
   * @param userId ID người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns Thông tin shop đã cập nhật
   */
  @Transactional()
  async updateShopInfo(userId: number, updateDto: UpdateUserShopInfoDto): Promise<UserShopInfoResponseDto> {
    try {
      this.logger.log(`Cập nhật thông tin shop cho userId: ${userId}`);
      
      // Kiểm tra shop info có tồn tại không
      const existingShopInfo = await this.userShopInfoRepository.findByUserId(userId);
      if (!existingShopInfo) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Thông tin shop không tồn tại. Vui lòng tạo mới trước.'
        );
      }

      const shopInfo = await this.userShopInfoRepository.updateByUserId(userId, updateDto);

      this.logger.log(`Đã cập nhật thông tin shop cho userId: ${userId}`);
      return plainToInstance(UserShopInfoResponseDto, shopInfo, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
        `Lỗi khi cập nhật thông tin shop: ${error.message}`
      );
    }
  }

  /**
   * Xóa thông tin shop
   * @param userId ID người dùng
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteShopInfo(userId: number): Promise<boolean> {
    try {
      this.logger.log(`Xóa thông tin shop cho userId: ${userId}`);
      
      const deleted = await this.userShopInfoRepository.deleteByUserId(userId);
      
      if (!deleted) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Không tìm thấy thông tin shop để xóa'
        );
      }

      this.logger.log(`Đã xóa thông tin shop cho userId: ${userId}`);
      return true;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_DELETION_FAILED,
        `Lỗi khi xóa thông tin shop: ${error.message}`
      );
    }
  }

  /**
   * Lấy tên shop hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns Tên shop
   */
  async getShopName(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopName || 'Cửa hàng';
    } catch (error) {
      this.logger.warn(`Không thể lấy tên shop cho userId ${userId}: ${error.message}`);
      return 'Cửa hàng';
    }
  }

  /**
   * Lấy số điện thoại shop hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns Số điện thoại shop
   */
  async getShopPhone(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopPhone || '0123456789';
    } catch (error) {
      this.logger.warn(`Không thể lấy số điện thoại shop cho userId ${userId}: ${error.message}`);
      return '0123456789';
    }
  }

  /**
   * Lấy địa chỉ shop hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns Địa chỉ shop
   */
  async getShopAddress(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopAddress || 'Số 1, Quận 1, TP.HCM';
    } catch (error) {
      this.logger.warn(`Không thể lấy địa chỉ shop cho userId ${userId}: ${error.message}`);
      return 'Số 1, Quận 1, TP.HCM';
    }
  }

  /**
   * Lấy tỉnh/thành phố shop hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns Tỉnh/thành phố shop
   */
  async getShopProvince(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopProvince || 'Hồ Chí Minh';
    } catch (error) {
      this.logger.warn(`Không thể lấy tỉnh shop cho userId ${userId}: ${error.message}`);
      return 'Hồ Chí Minh';
    }
  }

  /**
   * Lấy quận/huyện shop hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns Quận/huyện shop
   */
  async getShopDistrict(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopDistrict || 'Quận 1';
    } catch (error) {
      this.logger.warn(`Không thể lấy quận shop cho userId ${userId}: ${error.message}`);
      return 'Quận 1';
    }
  }

  /**
   * Lấy phường/xã shop hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns Phường/xã shop
   */
  async getShopWard(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopWard || 'Phường Bến Nghé';
    } catch (error) {
      this.logger.warn(`Không thể lấy phường shop cho userId ${userId}: ${error.message}`);
      return 'Phường Bến Nghé';
    }
  }

  /**
   * Lấy GHN District ID hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns GHN District ID
   */
  async getGhnDistrictId(userId: number): Promise<number> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.ghnDistrictId || 1442; // Mặc định Quận 1, TP.HCM
    } catch (error) {
      this.logger.warn(`Không thể lấy GHN District ID cho userId ${userId}: ${error.message}`);
      return 1442;
    }
  }

  /**
   * Lấy GHN Ward Code hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns GHN Ward Code
   */
  async getGhnWardCode(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.ghnWardCode || '21211'; // Mặc định Phường Bến Nghé
    } catch (error) {
      this.logger.warn(`Không thể lấy GHN Ward Code cho userId ${userId}: ${error.message}`);
      return '21211';
    }
  }

  /**
   * Kiểm tra shop info có tồn tại không
   * @param userId ID người dùng
   * @returns True nếu tồn tại
   */
  async hasShopInfo(userId: number): Promise<boolean> {
    try {
      return await this.userShopInfoRepository.exists(userId);
    } catch (error) {
      this.logger.warn(`Không thể kiểm tra shop info cho userId ${userId}: ${error.message}`);
      return false;
    }
  }
}
