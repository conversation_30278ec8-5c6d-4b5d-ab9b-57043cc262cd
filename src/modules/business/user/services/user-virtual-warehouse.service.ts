import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { PaginatedResult } from '@common/response';
import { Transactional } from 'typeorm-transactional';
import { VirtualWarehouse } from '@modules/business/entities';
import {
  VirtualWarehouseRepository,
} from '@modules/business/repositories';
import { ValidationHelper } from '../helpers/validation.helper';
import {
  CreateVirtualWarehouseDto,
  UpdateVirtualWarehouseDto,
  VirtualWarehouseResponseDto,
  QueryVirtualWarehouseDto,
} from '../dto/warehouse';

/**
 * Service xử lý nghiệp vụ liên quan đến kho
 */
@Injectable()
export class UserVirtualWarehouseService {
  private readonly logger = new Logger(UserVirtualWarehouseService.name);

  constructor(
    private readonly virtualWarehouseRepository: VirtualWarehouseRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo mới kho ảo
   * @param warehouseId ID của kho
   * @param createDto DTO chứa thông tin tạo kho ảo mới
   * @returns Thông tin kho ảo đã tạo
   */
  @Transactional()
  async createVirtualWarehouse(
    warehouseId: number,
    createDto: CreateVirtualWarehouseDto,
  ): Promise<VirtualWarehouseResponseDto> {
    try {
      // Kiểm tra dữ liệu đầu vào
      await this.validationHelper.validateCreateVirtualWarehouse(
        createDto,
        warehouseId,
      );

      // Tạo kho ảo
      const virtualWarehouse = new VirtualWarehouse();
      virtualWarehouse.warehouseId = warehouseId;
      virtualWarehouse.associatedSystem = createDto.associatedSystem || ' ';
      virtualWarehouse.purpose = createDto.purpose || ' ';

      // Lưu kho ảo vào cơ sở dữ liệu
      const savedVirtualWarehouse =
        await this.virtualWarehouseRepository.createVirtualWarehouse(
          virtualWarehouse,
        );

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(
        VirtualWarehouseResponseDto,
        savedVirtualWarehouse,
        {
          excludeExtraneousValues: true,
        },
      );
    } catch (error) {
      this.logger.error(`Lỗi khi tạo kho ảo: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_CREATION_FAILED,
        `Lỗi khi tạo kho ảo: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin kho ảo
   * @param warehouseId ID của kho
   * @param updateDto DTO chứa thông tin cập nhật kho ảo
   * @param userId ID của người dùng
   * @returns Thông tin kho ảo đã cập nhật
   */
  @Transactional()
  async updateVirtualWarehouse(
    warehouseId: number,
    updateDto: UpdateVirtualWarehouseDto,
    userId: number,
  ): Promise<VirtualWarehouseResponseDto> {
    try {
      // Kiểm tra dữ liệu đầu vào
      await this.validationHelper.validateUpdateVirtualWarehouse(
        warehouseId,
        updateDto,
      );

      // Lấy thông tin kho để kiểm tra quyền truy cập
      const warehouse = await this.validationHelper.validateWarehouseExists(warehouseId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by

      // Chuẩn bị dữ liệu cập nhật
      const updateData: Partial<VirtualWarehouse> = {};

      if (updateDto.associatedSystem !== undefined) {
        updateData.associatedSystem = updateDto.associatedSystem;
      }

      if (updateDto.purpose !== undefined) {
        updateData.purpose = updateDto.purpose;
      }

      // Cập nhật kho ảo
      await this.virtualWarehouseRepository.updateVirtualWarehouse(
        warehouseId,
        updateData,
      );

      // Lấy thông tin đầy đủ của kho ảo
      const virtualWarehouseWithDetails =
        await this.virtualWarehouseRepository.findByWarehouseIdWithDetails(
          warehouseId,
        );

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(
        VirtualWarehouseResponseDto,
        virtualWarehouseWithDetails,
        {
          excludeExtraneousValues: true,
        },
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật kho ảo: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_UPDATE_FAILED,
        `Lỗi khi cập nhật kho ảo: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin kho ảo theo ID
   * @param warehouseId ID của kho
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của kho ảo
   */
  async getVirtualWarehouseById(
    warehouseId: number,
    userId: number,
  ): Promise<VirtualWarehouseResponseDto> {
    try {
      // Lấy thông tin đầy đủ của kho ảo (kết hợp với thông tin kho chung và trường tùy chỉnh)
      const virtualWarehouseWithDetails =
        await this.virtualWarehouseRepository.findByWarehouseIdWithDetails(
          warehouseId,
        );

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity VirtualWarehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity VirtualWarehouse hoặc sử dụng trường khác để xác định quyền sở hữu
      if (!virtualWarehouseWithDetails) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
          `Không tìm thấy thông tin kho ảo với ID ${warehouseId}`,
        );
      }

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(
        VirtualWarehouseResponseDto,
        virtualWarehouseWithDetails,
        {
          excludeExtraneousValues: true,
        },
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thông tin kho ảo: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_FETCH_FAILED,
        `Lỗi khi lấy thông tin kho ảo: ${error.message}`,
      );
    }
  }

  /**
   * Xóa kho ảo
   * @param warehouseId ID của kho
   * @param userId ID của người dùng
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteVirtualWarehouse(warehouseId: number, userId: number): Promise<void> {
    try {
      // Kiểm tra kho ảo tồn tại
      await this.validationHelper.validateVirtualWarehouseExists(warehouseId);

      // Lấy thông tin kho để kiểm tra quyền truy cập
      const warehouse = await this.validationHelper.validateWarehouseExists(warehouseId);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Warehouse không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Warehouse hoặc sử dụng trường created_by

      // Xóa kho ảo
      const result =
        await this.virtualWarehouseRepository.deleteVirtualWarehouse(
          warehouseId,
        );

      if (!result) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_DELETE_FAILED,
          `Không thể xóa kho ảo với ID ${warehouseId}`,
        );
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa kho ảo: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_DELETE_FAILED,
        `Lỗi khi xóa kho ảo: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách kho ảo với phân trang và lọc
   * @param queryDto Tham số truy vấn
   * @returns Danh sách kho ảo với phân trang
   */
  async getVirtualWarehouses(
    queryDto: QueryVirtualWarehouseDto,
  ): Promise<PaginatedResult<VirtualWarehouseResponseDto>> {
    try {
      // Kiểm tra và xử lý trường sắp xếp
      const validSortFields = ['warehouseId', 'name', 'description', 'associatedSystem', 'purpose'];
      if (queryDto.sortBy && !validSortFields.includes(queryDto.sortBy)) {
        // Nếu trường sắp xếp không hợp lệ, sử dụng trường mặc định
        queryDto.sortBy = 'warehouseId';
      }

      // Đảm bảo chỉ lấy kho của người dùng hiện tại
      if (!queryDto.userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.WAREHOUSE_ACCESS_DENIED,
          `Không thể lấy danh sách kho ảo: Thiếu thông tin người dùng`
        );
      }

      // Lấy danh sách kho ảo từ repository
      const result = await this.virtualWarehouseRepository.findAll(queryDto);

      // Lấy thông tin chi tiết cho mỗi kho ảo
      const detailedItems = await Promise.all(
        result.items.map(async (virtualWarehouse) => {
          const details =
            await this.virtualWarehouseRepository.findByWarehouseIdWithDetails(
              virtualWarehouse.warehouseId,
            );
          return details;
        }),
      );

      // Chuyển đổi các item sang DTO
      const items = detailedItems.map((item) =>
        plainToInstance(VirtualWarehouseResponseDto, item, {
          excludeExtraneousValues: true,
        }),
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách kho ảo: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_FETCH_FAILED,
        `Lỗi khi lấy danh sách kho ảo: ${error.message}`,
      );
    }
  }
}
