import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo kho vật lý
 */
export class CreatePhysicalWarehouseDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  @Type(() => Number)
  userId?: number;
  
  /**
   * ID của kho
   * @example 1
   */
  @ApiProperty({
    description: 'ID của kho',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID kho không được để trống' })
  @IsNumber({}, { message: 'ID kho phải là số' })
  @Type(() => Number)
  warehouseId: number;

  /**
   * Địa chỉ kho
   * @example "123 Đường ABC, Quận 1, TP.HCM"
   */
  @ApiProperty({
    description: 'Địa chỉ kho',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Địa chỉ kho không được để trống' })
  @IsString({ message: 'Địa chỉ kho phải là chuỗi' })
  @MaxLength(255, { message: 'Địa chỉ kho không được vượt quá 255 ký tự' })
  address: string;

  /**
   * Sức chứa kho
   * @example 1000
   */
  @ApiProperty({
    description: 'Sức chứa kho',
    example: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Sức chứa kho phải là số' })
  @Type(() => Number)
  capacity?: number;
}
